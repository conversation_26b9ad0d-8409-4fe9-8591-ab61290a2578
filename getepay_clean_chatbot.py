import streamlit as st
import boto3
import json
import pandas as pd
import os
import uuid
from datetime import datetime
from textblob import TextBlob
import re
import config
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize CSV for lead data
DATA_CSV = "getepay_leads.csv"

def initialize_csv():
    """Initialize CSV file for lead data"""
    if not os.path.exists(DATA_CSV):
        df = pd.DataFrame(columns=[
            'timestamp', 'session_id', 'name', 'email', 'phone', 'company_name',
            'business_type', 'messages', 'sentiment_score', 'services_interested',
            'lead_score', 'requirement_summary'
        ])
        df.to_csv(DATA_CSV, index=False)

        # Add some genuine demo data for presentation
        demo_data = [
            {
                'timestamp': '2025-01-27 09:15:23',
                'session_id': 'demo_001',
                'messages': '[{"role": "user", "content": "I need payment gateway for my restaurant"}, {"role": "assistant", "content": "Great! Getepay offers perfect restaurant solutions..."}]',
                'sentiment_score': 0.65,
                'business_type': 'Restaurant',
                'services_interested': 'Payment Gateway, QR Payments',
                'lead_score': 85,
                'email': '<EMAIL>',
                'phone': '+91 9876543210',
                'company_name': 'Tasty Foods Restaurant',
                'requirement_summary': 'Needs payment gateway and QR payments for restaurant'
            },
            {
                'timestamp': '2025-01-27 10:30:45',
                'session_id': 'demo_002',
                'messages': '[{"role": "user", "content": "PayPal is giving me errors, need alternative"}, {"role": "assistant", "content": "I understand your frustration with PayPal..."}]',
                'sentiment_score': -0.25,
                'business_type': 'E-commerce',
                'services_interested': 'Payment Gateway, API Integration',
                'lead_score': 72,
                'email': '<EMAIL>',
                'phone': '+91 8765432109',
                'company_name': 'ShopNow E-commerce',
                'requirement_summary': 'Frustrated with PayPal, needs reliable payment gateway'
            },
            {
                'timestamp': '2025-01-27 11:45:12',
                'session_id': 'demo_003',
                'messages': '[{"role": "user", "content": "Want to build payment app for my customers"}, {"role": "assistant", "content": "Excellent! Getepay can create a branded mobile app..."}]',
                'sentiment_score': 0.45,
                'business_type': 'Services',
                'services_interested': 'Mobile App, Payment Gateway',
                'lead_score': 90,
                'email': '<EMAIL>',
                'phone': '+91 7654321098',
                'company_name': 'Digital Services Ltd',
                'requirement_summary': 'Wants custom payment app for customers'
            }
        ]

        demo_df = pd.DataFrame(demo_data)
        df = pd.concat([df, demo_df], ignore_index=True)
        df.to_csv(DATA_CSV, index=False)

def get_bedrock_client():
    """Initialize Bedrock client"""
    return boto3.client(
        'bedrock-runtime',
        region_name=config.AWS_REGION,
        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
    )

def analyze_sentiment(text):
    """Accurate sentiment analysis based on context and keywords"""
    if not text or text.strip() == "":
        return 0.0

    text_lower = text.lower()

    # Strong negative indicators
    strong_negative = ['frustrated', 'frustated', 'hate', 'terrible', 'awful', 'worst', 'horrible', 'disgusting', 'pathetic', 'useless']

    # Moderate negative indicators
    moderate_negative = ['bad', 'disappointed', 'problem', 'issue', 'error', 'stuck', 'wrong', 'failed', 'broken', "don't like", "dont like", "didn't like", "didnt like", 'not good', 'poor']

    # Positive indicators
    positive_words = ['great', 'good', 'excellent', 'amazing', 'love', 'like', 'happy', 'satisfied', 'perfect', 'awesome', 'fantastic', 'wonderful', 'impressed', 'nice', 'thanks', 'thank you']

    # Interest indicators (slightly positive)
    interest_words = ['interested', 'want', 'need', 'looking for', 'tell me', 'show me', 'help me']

    # Neutral/question words
    neutral_words = ['what', 'how', 'when', 'where', 'why', 'can', 'could', 'would', 'should', 'hi', 'hello', 'hey']

    # Count matches
    strong_neg_count = sum(1 for word in strong_negative if word in text_lower)
    moderate_neg_count = sum(1 for word in moderate_negative if word in text_lower)
    positive_count = sum(1 for word in positive_words if word in text_lower)
    interest_count = sum(1 for word in interest_words if word in text_lower)
    neutral_count = sum(1 for word in neutral_words if word in text_lower)

    # Calculate sentiment with proper weighting
    if strong_neg_count > 0:
        return -0.8  # Very negative
    elif moderate_neg_count > 0 and positive_count == 0:
        return -0.4  # Negative
    elif positive_count > 0 and moderate_neg_count == 0:
        return 0.6 + min(positive_count * 0.1, 0.3)  # Positive
    elif interest_count > 0:
        return 0.3  # Interested/engaged
    elif neutral_count > 0:
        return 0.1  # Neutral but engaged
    else:
        # Use TextBlob as fallback
        try:
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            return polarity if polarity != 0 else 0.1
        except:
            return 0.1

def get_sentiment_display(sentiment_score):
    """Get display info for sentiment score"""
    if sentiment_score > 0.3:
        return {
            'emoji': '😊',
            'label': 'Positive',
            'color': '#4CAF50',
            'tip': 'Customer is interested! Great opportunity.'
        }
    elif sentiment_score > 0:
        return {
            'emoji': '🙂',
            'label': 'Slightly Positive',
            'color': '#8BC34A',
            'tip': 'Customer is showing some interest.'
        }
    elif sentiment_score > -0.2:
        return {
            'emoji': '😐',
            'label': 'Neutral',
            'color': '#FF9800',
            'tip': 'Customer is asking questions.'
        }
    else:
        return {
            'emoji': '😔',
            'label': 'Negative',
            'color': '#F44336',
            'tip': 'Customer seems frustrated. Address concerns.'
        }



def extract_contact_info(messages):
    """Extract email and phone from messages"""
    text = " ".join([msg["content"] for msg in messages if msg["role"] == "user"])
    
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    phone_pattern = r'(\+91|91)?[\s-]?[6-9]\d{9}'
    
    emails = re.findall(email_pattern, text)
    phones = re.findall(phone_pattern, text)
    
    return emails[0] if emails else "", phones[0] if phones else ""

def calculate_lead_score(messages, sentiment_score):
    """Calculate lead quality score"""
    text = " ".join([msg["content"] for msg in messages if msg["role"] == "user"])

    # Interest indicators
    interest_keywords = ["interested", "pricing", "cost", "integrate", "setup", "demo", "trial", "want", "need"]
    interest_score = sum(1 for keyword in interest_keywords if keyword.lower() in text.lower()) * 10

    # Sentiment score (convert -1 to 1 range to 0-50)
    sentiment_points = max(0, (sentiment_score + 1) * 25)

    # Contact info provided
    email, phone = extract_contact_info(messages)
    contact_score = 30 if email or phone else 0

    total_score = min(interest_score + sentiment_points + contact_score, 100)
    return max(total_score, 15)  # Minimum score of 15

def get_getepay_response(prompt, max_tokens=150):
    """Get response from Bedrock for Getepay queries"""
    bedrock = get_bedrock_client()
    
    getepay_context = """You are Getepay's AI assistant. Getepay is a complete payment solution for businesses.

Key Services:
- Payment Gateway (UPI, Cards, Wallets, Net Banking)
- QR Code Payments (Static & Dynamic QR codes)
- Payment Links & Buttons
- Mobile Apps for businesses
- Real-time Analytics & Reporting
- API Integration (REST APIs, Webhooks)
- Sales Management (Digital Khata)

QR Code Payment Solutions:
- Static QR codes for fixed amounts
- Dynamic QR codes for variable amounts
- Bulk QR code generation
- QR code customization with logo
- Real-time payment notifications
- Integration with POS systems

Key Benefits:
- 24/7 payment collection
- Instant activation (same day)
- No coding required (plug & play)
- Bank-grade security (AES 256-bit encryption)
- Multiple payment options (UPI, Cards, Wallets)
- Real-time reporting & analytics
- 99.9% uptime guarantee

Pricing: Competitive rates starting from 1.5% per transaction
Setup: Free setup, no hidden charges

Contact: +91 ********** | <EMAIL>

Keep responses short, helpful, and focused on Getepay solutions. Always mention specific benefits. Ask for contact details when appropriate."""

    formatted_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>
{getepay_context}
<|eot_id|><|start_header_id|>user<|end_header_id|>
{prompt.strip()}
<|eot_id|><|start_header_id|>assistant<|end_header_id|>
"""

    body = json.dumps({
        "prompt": formatted_prompt,
        "max_gen_len": max_tokens,
        "temperature": 0.3,
        "top_p": 0.9
    })

    try:
        response = bedrock.invoke_model(
            modelId=config.MODEL_ID,
            body=body
        )
        response_body = json.loads(response.get('body').read())
        return response_body
    except Exception as e:
        st.error(f"Error: {e}")
        return None

def save_lead_data(session_id, messages):
    """Save lead data to CSV with user info"""
    if len(messages) < 2:  # Need at least user message and bot response
        return

    # Get user info from session state
    user_info = st.session_state.get('user_info', {})

    # Analyze conversation
    user_messages = [msg["content"] for msg in messages if msg["role"] == "user"]
    all_text = " ".join(user_messages)

    sentiment_score = analyze_sentiment(all_text)
    lead_score = calculate_lead_score(messages, sentiment_score)

    # Use user info for contact details, fallback to extraction
    email = user_info.get('email', '')
    phone = user_info.get('phone', '')
    if not email or not phone:
        extracted_email, extracted_phone = extract_contact_info(messages)
        email = email or extracted_email
        phone = phone or extracted_phone

    # Use user info for business type, fallback to detection
    business_type = user_info.get('business_type', 'Other')
    if business_type == 'Other':
        business_keywords = {
            "restaurant": ["restaurant", "food", "cafe", "hotel"],
            "ecommerce": ["website", "online", "ecommerce", "store"],
            "retail": ["shop", "retail", "store", "business"],
            "services": ["service", "consultant", "agency"]
        }

        for btype, keywords in business_keywords.items():
            if any(keyword in all_text.lower() for keyword in keywords):
                business_type = btype.title()
                break

    # Extract interested services
    service_keywords = {
        "Payment Gateway": ["gateway", "payment", "online payment"],
        "QR Payments": ["qr", "qr code", "scan"],
        "Mobile App": ["app", "mobile", "application"],
        "API Integration": ["api", "integrate", "integration"]
    }

    interested_services = []
    for service, keywords in service_keywords.items():
        if any(keyword in all_text.lower() for keyword in keywords):
            interested_services.append(service)

    # Create summary
    requirement_summary = f"Interested in {', '.join(interested_services) if interested_services else 'payment solutions'}"

    # Save to CSV with user info
    new_data = {
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'session_id': session_id,
        'name': user_info.get('name', ''),
        'email': email,
        'phone': phone,
        'company_name': user_info.get('company', ''),
        'business_type': business_type,
        'messages': json.dumps(messages),
        'sentiment_score': round(sentiment_score, 2),
        'services_interested': ', '.join(interested_services),
        'lead_score': lead_score,
        'requirement_summary': requirement_summary
    }

    df = pd.read_csv(DATA_CSV)
    df = pd.concat([df, pd.DataFrame([new_data])], ignore_index=True)
    df.to_csv(DATA_CSV, index=False)

def collect_user_info():
    """Collect user information before starting chat"""
    st.markdown("""
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 2rem; border-radius: 15px; margin-bottom: 1.5rem; text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
        <h2 style="color: white; margin: 0; font-size: 2rem; font-weight: 700;">
            👋 Welcome to Getepay AI Intelligence Hub
        </h2>
        <p style="color: white; margin: 10px 0 0 0; font-size: 1.1rem; opacity: 0.9;">
            Please provide your details to get personalized assistance
        </p>
    </div>
    """, unsafe_allow_html=True)

    with st.form("user_info_form"):
        col1, col2 = st.columns(2)

        with col1:
            name = st.text_input("👤 Full Name *", placeholder="Enter your full name")
            email = st.text_input("📧 Email Address *", placeholder="<EMAIL>")

        with col2:
            # Phone number with country code in same row
            st.markdown("📱 **Phone Number** *")
            phone_col1, phone_col2 = st.columns([1, 2])
            with phone_col1:
                country_codes = {
                    "+91": "+91",
                    "+1": "+1",
                    "+44": "+44",
                    "+61": "+61",
                    "+49": "+49",
                    "+33": "+33",
                    "+81": "+81",
                    "+65": "+65",
                    "+971": "+971"
                }
                country_code = st.selectbox("Code", list(country_codes.keys()), index=0, label_visibility="collapsed")

            with phone_col2:
                phone_number = st.text_input("Number", placeholder="XXXXXXXXXX", label_visibility="collapsed")

            phone = f"{country_code} {phone_number}" if phone_number else ""

            company = st.text_input("🏢 Company Name", placeholder="Your company name")

        business_type = st.selectbox(
            "🏪 Business Type",
            ["E-commerce", "Retail", "Restaurant", "Healthcare", "Education", "Services", "Manufacturing", "Other"]
        )

        submitted = st.form_submit_button("🚀 Start Chat", use_container_width=True)

        if submitted:
            if name and email and phone:
                st.session_state.user_info = {
                    "name": name,
                    "email": email,
                    "phone": phone,
                    "company": company,
                    "business_type": business_type
                }
                st.session_state.info_collected = True
                st.rerun()
            else:
                st.error("Please fill in all required fields (Name, Email, Phone)")

def main():
    # Page config
    st.set_page_config(
        page_title="Getepay AI Assistant",
        page_icon="💳",
        layout="centered"
    )

    # Initialize CSV
    initialize_csv()

    # Initialize session
    if 'session_id' not in st.session_state:
        st.session_state.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

    if "info_collected" not in st.session_state:
        st.session_state.info_collected = False

    if "user_info" not in st.session_state:
        st.session_state.user_info = {}

    # Show user info form if not collected
    if not st.session_state.info_collected:
        collect_user_info()
        return
    
    # Clean header
    st.title("🚀 Getepay AI Intelligence Hub")
    st.markdown("**Advanced Payment Solutions & Customer Support**")

    # Initialize chat
    if 'messages' not in st.session_state:
        user_name = st.session_state.get('user_info', {}).get('name', 'there')
        st.session_state.messages = [
            {
                "role": "assistant",
                "content": f"""👋 **Hello {user_name}!** Welcome to Getepay AI Intelligence Hub! 🚀

I'm your dedicated payment solutions assistant, powered by advanced AI to provide you with personalized support.

**🎯 How I can help you today:**

🔹 **Payment Gateway Integration** - Seamless setup and configuration
🔹 **QR Code Payment Solutions** - Quick scan-to-pay implementations
🔹 **Mobile App Development** - Custom payment app solutions
🔹 **API Integration Support** - Technical guidance and documentation
🔹 **Business Consultation** - Payment strategy and optimization

**💡 Quick Start:** Tell me about your business or select from common challenges below!

What payment challenge would you like to solve today? I'm here to provide expert guidance! ✨"""
            }
        ]

    # Display messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # Add quick problem templates - only show on first load
    if len(st.session_state.messages) == 1:
        st.markdown("### 💡 Common Payment Challenges - Click to Get Instant Solutions")

        # Create buttons for common problems
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔒 Payment Security Issues", use_container_width=True):
                st.session_state.messages.append({"role": "user", "content": "I'm concerned about payment security and fraud prevention. How can Getepay help secure my transactions?"})
                st.rerun()

            if st.button("📱 Mobile Payment Integration", use_container_width=True):
                st.session_state.messages.append({"role": "user", "content": "I need to integrate mobile payments into my app. What are the best practices and APIs available?"})
                st.rerun()

        with col2:
            if st.button("💳 High Transaction Fees", use_container_width=True):
                st.session_state.messages.append({"role": "user", "content": "My current payment processor charges high fees. How can Getepay help reduce my transaction costs?"})
                st.rerun()

            if st.button("🌐 Multi-Currency Support", use_container_width=True):
                st.session_state.messages.append({"role": "user", "content": "I need to accept payments in multiple currencies for my international business. What solutions does Getepay offer?"})
                st.rerun()

        with col3:
            if st.button("⚡ Slow Payment Processing", use_container_width=True):
                st.session_state.messages.append({"role": "user", "content": "My payments take too long to process, affecting customer experience. How can I speed up payment processing?"})
                st.rerun()

            if st.button("📊 Payment Analytics", use_container_width=True):
                st.session_state.messages.append({"role": "user", "content": "I need better insights into my payment data and customer behavior. What analytics tools does Getepay provide?"})
                st.rerun()

    # Voice input and chat input
    col_voice, col_text = st.columns([1, 4])

    with col_voice:
        # Voice input button
        if st.button("🎤", help="Click to speak", use_container_width=True):
            st.info("🎤 Voice input feature coming soon! For now, please type your message below.")

    with col_text:
        # Chat input
        if prompt := st.chat_input("💬 Tell me about your payment needs... or click 🎤 to speak"):
            # Analyze sentiment immediately
            sentiment_score = analyze_sentiment(prompt)
            sentiment_info = get_sentiment_display(sentiment_score)

            # Add user message
            st.session_state.messages.append({"role": "user", "content": prompt})
            with st.chat_message("user"):
                st.markdown(prompt)

            # Show sentiment analysis for this message
            st.markdown(f"""
            <div style="background: {sentiment_info['color']}; padding: 8px 12px; border-radius: 12px;
                        color: white; font-size: 12px; margin-top: 5px; text-align: center;">
                {sentiment_info['emoji']} {sentiment_info['label']} ({sentiment_score:.2f})
            </div>
            """, unsafe_allow_html=True)

            # Get bot response
            with st.chat_message("assistant"):
                response = get_getepay_response(prompt)

                if response and 'generation' in response:
                    bot_response = response['generation'].strip()
                    st.markdown(bot_response)
                    st.session_state.messages.append({"role": "assistant", "content": bot_response})

                    # Show AI tip
                    st.markdown(f"""
                    <div style="background: linear-gradient(135deg, #667eea, #764ba2);
                                padding: 8px; border-radius: 8px; color: white; margin-top: 8px; font-size: 11px;">
                        💡 <strong>AI Tip:</strong> {sentiment_info['tip']}
                    </div>
                    """, unsafe_allow_html=True)

                    # Save lead data
                    save_lead_data(st.session_state.session_id, st.session_state.messages)

                    # Refresh analytics
                    st.rerun()



        # Professional Footer
        st.markdown("---")
        st.markdown("### 💳 GetePay - Complete Payment Solutions")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            **🏢 Company Info**

            Getepay is a complete merchant solution that enables small or big business owners to go online with their own website/Mobile APP pre-integrated with a business management solution and Payment Gateway.
            """)

        with col2:
            st.markdown("""
            **📍 Our Location**

            Futuretek Commerce Pvt Ltd
            181, Gopi Nagar, Sanganer, Jaipur
            Rajasthan - 302039

            **Ph:** +91 **********
            **Email:** <EMAIL>
            """)

        with col3:
            st.markdown("""
            **🔗 Important Links**

            • About Us
            • Contact Us
            • Partners
            • Life at Getepay
            • Privacy Policy
            • Terms & Conditions
            """)

        st.markdown("---")
        st.markdown(
            "<div style='text-align: center; color: #666;'>© Copyright 2024. All Rights Reserved. | Powered by Getepay AI Intelligence Hub</div>",
            unsafe_allow_html=True
        )

if __name__ == "__main__":
    main()
