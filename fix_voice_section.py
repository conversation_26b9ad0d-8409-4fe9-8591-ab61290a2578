#!/usr/bin/env python3
"""
<PERSON>ript to fix the voice section and emoji issues in the chatbot
"""

def fix_chatbot_file():
    with open('getepay_simple_chatbot.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove the voice input section
    lines = content.split('\n')
    new_lines = []
    skip_voice_section = False
    
    for i, line in enumerate(lines):
        if '# Voice input section removed as requested' in line:
            skip_voice_section = True
            new_lines.append('        # Voice input section removed')
            continue
        elif skip_voice_section and '# Chat input - Use st.chat_input for better UX' in line:
            skip_voice_section = False
            new_lines.append('        # Chat input - Direct input without voice features')
            continue
        elif skip_voice_section:
            continue
        else:
            new_lines.append(line)
    
    # Write back the fixed content
    with open('getepay_simple_chatbot.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print("Fixed voice section and emoji issues!")

if __name__ == "__main__":
    fix_chatbot_file()
