# AWS Bedrock Configuration
# Copy this file to .env and fill in your actual AWS credentials

# Your AWS Access Key ID
AWS_ACCESS_KEY_ID=your_access_key_here

# Your AWS Secret Access Key  
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# AWS Region (default: us-east-1)
AWS_REGION=us-east-1

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Edit .env with your actual AWS credentials
# 3. Make sure your AWS account has Bedrock access
# 4. Ensure the region supports Claude models
