# 🚀 Getepay AI Chatbot - Client Demo

An intelligent AI-powered chatbot specifically designed for **Getepay** - a complete merchant payment solution company. This chatbot demonstrates advanced lead generation, customer interaction, and analytics capabilities.

## 🌟 Key Features

### 🤖 AI-Powered Conversations
- **Meta Llama 3** integration via AWS Bedrock
- Getepay-specific knowledge base and context
- Natural conversation flow with lead qualification

### 📊 Advanced Analytics & Data Collection
- **Automatic CSV data storage** for user interactions
- **Sentiment analysis** of customer conversations
- **Lead quality scoring** (0-100 scale)
- **Business intelligence** extraction from conversations

### 🎯 Lead Management
- **Contact information extraction** (email, phone)
- **Business type classification** (retail, technology, restaurant, etc.)
- **Pain point identification** and interest tracking
- **Follow-up prioritization** based on engagement

### 📈 Real-time Dashboard
- Live analytics display within the chat interface
- Lead distribution charts and metrics
- Conversion likelihood tracking
- Recent leads overview

## 🏢 Getepay Integration

The chatbot is specifically designed for **Getepay** with:

### 🔧 Service Knowledge
- Payment Gateway Integration
- Mobile App Development
- QR Code Payments
- Real-time Analytics
- Sales Management Tools
- API Integration capabilities

### 🏪 Target Industries
- **E-commerce** stores
- **Retail** businesses  
- **Restaurants** and hospitality
- **Service** providers
- **Technology** companies
- **Healthcare** and education

### 📞 Contact Integration
- Phone: +91 **********
- Email: <EMAIL>
- Working Hours: Mon-Sat 09:00-18:30

## 📋 Data Collection Schema

### User Data CSV (`getepay_user_data.csv`)
| Field | Description |
|-------|-------------|
| timestamp | Conversation timestamp |
| session_id | Unique session identifier |
| name | Customer name (extracted) |
| email | Email address (extracted) |
| phone | Phone number (extracted) |
| business_type | Auto-classified business type |
| business_size | Small/Medium/Large classification |
| requirement_summary | AI-generated summary of needs |
| sentiment_score | Sentiment analysis (-1 to +1) |
| sentiment_label | Positive/Neutral/Negative |
| pain_points | Identified customer challenges |
| interested_features | Getepay features of interest |
| lead_quality_score | Overall lead score (0-100) |

### Stats CSV (`getepay_stats.csv`)
| Field | Description |
|-------|-------------|
| session_id | Session identifier |
| total_messages | Message count in conversation |
| conversation_duration | Time spent chatting |
| user_engagement_score | Engagement level (0-100) |
| topics_discussed | Key topics covered |
| conversion_likelihood | High/Medium/Low |
| follow_up_required | Yes/No recommendation |
| priority_level | Lead priority for sales team |

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Set Up Environment Variables
Create a `.env` file:
```env
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
```

### 3. Run the Demo
```bash
# Run sample data demo
python demo_csv_analytics.py

# Start the Getepay chatbot
streamlit run getepay_chatbot.py
```

### 4. Access the Interface
- Open browser to `http://localhost:8501`
- Start chatting to generate leads
- View analytics in the sidebar

## 📊 Analytics Dashboard

The chatbot includes a built-in analytics dashboard showing:

### 📈 Key Metrics
- **Total Leads** generated
- **High Quality Leads** (score > 70)
- **Contact Information** collection rate
- **Average Sentiment** score

### 📋 Business Intelligence
- **Business Type Distribution** chart
- **Feature Interest** analysis
- **Lead Quality** breakdown
- **Recent Leads** table

### 🎯 Sales Insights
- **Conversion Likelihood** scoring
- **Follow-up Priority** recommendations
- **Pain Point** identification
- **Feature Demand** tracking

## 🏆 Lead Quality Scoring Algorithm

The chatbot uses a sophisticated scoring system:

### 📞 Contact Information (30 points)
- Email provided: +15 points
- Phone provided: +15 points

### 🏢 Business Information (25 points)
- Business type identified: +15 points
- Business size classified: +10 points

### 💬 Engagement Level (25 points)
- 3+ messages exchanged: +15 points
- Detailed responses: +10 points

### 🎯 Interest Indicators (20 points)
- Keywords like "pricing", "demo", "integrate": +5 points each
- Maximum 20 points from interest signals

## 🎨 Customization for Clients

### 🎨 Branding
- **Color scheme** easily customizable
- **Logo and branding** integration ready
- **Company information** templated

### 🔧 Configuration
- **Industry-specific** responses
- **Service catalog** integration
- **Contact information** updates

### 📊 Analytics
- **Custom metrics** definition
- **Export capabilities** for CRM integration
- **Automated reporting** options

## 💼 Business Value

### 🎯 For Getepay Sales Team
- **Qualified leads** with detailed insights
- **Conversation summaries** for follow-up
- **Priority scoring** for time optimization
- **Contact information** automatically captured

### 📈 For Business Growth
- **24/7 lead generation** without human intervention
- **Customer insights** for service improvement
- **Market research** data from conversations
- **Conversion optimization** through analytics

### 🚀 Competitive Advantages
- **AI-powered qualification** vs manual chatbots
- **Integrated analytics** vs separate tools
- **Industry-specific** knowledge vs generic responses
- **Sentiment analysis** for customer satisfaction

## 🔮 Future Enhancements

### 🤖 AI Improvements
- **Multi-language** support
- **Voice integration** capabilities
- **Advanced NLP** for better understanding
- **Predictive analytics** for lead scoring

### 📊 Analytics Expansion
- **CRM integration** (Salesforce, HubSpot)
- **Email automation** for follow-ups
- **Advanced reporting** and dashboards
- **API endpoints** for external access

### 🎯 Lead Management
- **Automated scheduling** for demos
- **Quote generation** integration
- **Document sharing** capabilities
- **Video call** scheduling

## 📞 Support & Contact

For technical support or customization requests:
- **Email**: <EMAIL>
- **Phone**: +91 **********
- **Working Hours**: Monday-Saturday 09:00-18:30

---

**Built with ❤️ for Getepay - Empowering businesses with intelligent payment solutions**
