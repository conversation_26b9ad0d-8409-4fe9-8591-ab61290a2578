"""
Configuration settings for the AI Chatbot application.
"""

import os
from typing import Dict, Any

# AWS Bedrock Configuration
AWS_REGION = 'ap-south-1'
MODEL_ID = 'meta.llama3-8b-instruct-v1:0'

# Default model parameters for Meta Llama 3
DEFAULT_MODEL_PARAMS: Dict[str, Any] = {
    "max_gen_len": 512,
    "temperature": 0.5,
    "top_p": 0.9
}

# Streamlit Configuration
PAGE_CONFIG = {
    "page_title": "AI Chatbot with Meta Llama 3",
    "page_icon": "🤖",
    "layout": "wide"
}

# Token limits
MIN_TOKENS = 50
MAX_TOKENS = 2048
DEFAULT_TOKENS = 512

# Error messages
ERROR_MESSAGES = {
    "no_credentials": "AWS credentials not found. Please check your .env file.",
    "invalid_prompt": "Please enter a valid prompt.",
    "validation_error": "Invalid request parameters. Please try again.",
    "access_denied": "Access denied. Please check your AWS permissions.",
    "throttling": "Request throttled. Please wait a moment and try again.",
    "connection_error": "AWS connection error. Please check your internet connection.",
    "json_error": "Error parsing response from AI model.",
    "unexpected_error": "Unexpected error occurred. Please try again.",
    "no_response": "Sorry, I could not generate a response.",
    "processing_error": "Sorry, I encountered an error while processing your request. Please try again."
}

def validate_environment() -> bool:
    """Validate that required environment variables are set."""
    required_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY']
    return all(os.getenv(var) for var in required_vars)
