import streamlit as st
import pandas as pd
import os
from datetime import datetime
import uuid
import re
import boto3
import json
import asyncio
import websockets
from textblob import TextBlob
import io
import base64

# Configuration
DATA_CSV = "getepay_leads.csv"
AWS_REGION = "us-east-1"  # Change as needed

# AWS Bedrock Configuration with fallback
def initialize_bedrock():
    """Initialize Bedrock client with proper error handling"""
    try:
        # Try to initialize Bedrock client
        client = boto3.client(
            service_name='bedrock-runtime',
            region_name=AWS_REGION
        )
        # Test with a simple invoke (this will fail if no credentials, which is expected)
        # We'll just return the client if it initializes without error
        return client
    except Exception as e:
        print(f"AWS Bedrock not available: {e}")
        return None

bedrock_client = initialize_bedrock()

# Initialize CSV file with enhanced fields
def initialize_csv():
    if not os.path.exists(DATA_CSV):
        df = pd.DataFrame(columns=[
            'session_id', 'timestamp', 'name', 'email', 'phone', 'company_name',
            'business_type', 'services_interested', 'requirement_summary',
            'lead_score', 'sentiment_score', 'sentiment_label', 'sentiment_confidence',
            'customer_intent', 'urgency_level', 'conversation_data'
        ])
        df.to_csv(DATA_CSV, index=False)
    else:
        # Check if all required columns exist and add missing ones
        df = pd.read_csv(DATA_CSV)
        required_columns = [
            'session_id', 'timestamp', 'name', 'email', 'phone', 'company_name',
            'business_type', 'services_interested', 'requirement_summary',
            'lead_score', 'sentiment_score', 'sentiment_label', 'sentiment_confidence',
            'customer_intent', 'urgency_level', 'conversation_data'
        ]
        
        for col in required_columns:
            if col not in df.columns:
                df[col] = ''
        
        df.to_csv(DATA_CSV, index=False)

# Bedrock Response Generation
def generate_bedrock_response(query, user_info, conversation_history=None):
    """Generate intelligent responses using AWS Bedrock with conversation context"""
    if not bedrock_client:
        return generate_fallback_response(query, user_info, conversation_history)

    try:
        # Create context-aware prompt with conversation history
        user_context = ""
        if user_info:
            user_context = f"""
            User Information:
            - Name: {user_info.get('name', 'Unknown')}
            - Business Type: {user_info.get('business_type', 'Unknown')}
            - Company: {user_info.get('company', 'Unknown')}
            - Services Interested: {user_info.get('services', 'Payment gateway')}
            """

        # Add conversation context to avoid repetitive responses
        conversation_context = ""
        if conversation_history and len(conversation_history) > 1:
            recent_messages = conversation_history[-4:]  # Last 4 messages for context
            conversation_context = f"""
            Recent Conversation:
            {' | '.join([f"{msg['role']}: {msg['content']}" for msg in recent_messages])}

            IMPORTANT: Avoid repeating the same generic introduction. The user is already engaged in conversation.
            Provide a specific, contextual response based on their query and conversation history.
            """

        prompt = f"""
        You are a professional AI assistant for Getepay, a leading payment gateway company in India.

        {user_context}
        {conversation_context}

        Customer Query: "{query}"

        Please provide a helpful, professional response about Getepay's services. Include:
        1. Direct answer to their question
        2. Relevant Getepay features/benefits
        3. Next steps or call-to-action
        4. Keep it conversational and friendly
        5. DO NOT repeat generic introductions if this is an ongoing conversation

        Key Getepay Information:
        - Competitive rates: 1.5-2% per transaction
        - 99.9% uptime guarantee
        - Supports UPI, cards, net banking, wallets
        - Quick integration (24-48 hours)
        - 24/7 customer support
        - Contact: +91 **********, <EMAIL>
        - Advanced features: Multi-currency, recurring payments, analytics

        Response should be 2-3 sentences, professional but friendly.
        """

        body = json.dumps({
            "prompt": prompt,
            "max_tokens": 300,
            "temperature": 0.7
        })

        response = bedrock_client.invoke_model(
            body=body,
            modelId="anthropic.claude-3-sonnet-********-v1:0",
            accept="application/json",
            contentType="application/json"
        )

        response_body = json.loads(response.get('body').read())
        ai_response = response_body.get('completion', '').strip()

        return ai_response if ai_response else generate_fallback_response(query, user_info, conversation_history)

    except Exception as e:
        print(f"Bedrock response generation failed: {e}")
        return generate_fallback_response(query, user_info, conversation_history)

# Intelligent response generation
def generate_fallback_response(query, user_info, conversation_history=None):
    """Generate intelligent responses using local AI logic"""
    name = user_info.get('name', 'there') if user_info else 'there'
    business_type = user_info.get('business_type', 'business') if user_info else 'business'

    query_lower = query.lower()

    # QR Payment specific responses
    if any(word in query_lower for word in ['qr', 'qr payment', 'qr code', 'scan']):
        return f"Hi {name}! For QR payment issues, here's how Getepay can help: We provide dynamic QR codes that work with all UPI apps (PhonePe, GPay, Paytm). If you're facing QR payment problems, it could be due to network issues, expired QR codes, or app-specific problems. Our QR solution offers instant settlements and real-time notifications. Would you like me to help troubleshoot your specific QR payment issue?"

    elif any(word in query_lower for word in ['paypal', 'better than paypal', 'vs paypal']):
        return f"Great question, {name}! While PayPal is global, Getepay is specifically designed for Indian businesses with several advantages: 1) Lower transaction fees (1.5-2% vs PayPal's 3-4%), 2) Better UPI integration, 3) Faster settlements (T+1 vs T+3), 4) Local customer support in Hindi/English, 5) No international transaction fees for domestic payments. For Indian businesses, Getepay offers better rates, faster settlements, and local expertise. Would you like a detailed comparison?"

    elif any(word in query_lower for word in ['charge', 'fee', 'cost', 'price', 'rate']):
        return f"Hi {name}! Getepay offers competitive payment gateway rates starting at just 1.5-2% per transaction with no hidden fees. Our transparent pricing includes all major payment methods. Would you like a detailed quote for your {business_type}?"

    elif any(word in query_lower for word in ['integrate', 'setup', 'install', 'implement']):
        return f"Great question, {name}! Getepay integration is super quick - typically 24-48 hours for most platforms. We provide comprehensive APIs, plugins for popular e-commerce platforms, and dedicated technical support. Our team can help set up your {business_type} payment system seamlessly!"

    elif any(word in query_lower for word in ['upi', 'card', 'netbanking', 'wallet', 'payment method']):
        return f"Absolutely, {name}! Getepay supports all major payment methods - UPI, credit/debit cards, net banking, and digital wallets. We ensure 99.9% uptime and instant settlements. Perfect for your {business_type} to accept payments from all customers!"

    elif any(word in query_lower for word in ['support', 'help', 'customer service', 'assistance']):
        return f"We've got you covered, {name}! Getepay provides 24/7 customer support via phone, email, and chat. Our technical team is always ready to help with your {business_type} needs. Reach us at +91 ********** or <EMAIL>!"

    elif any(word in query_lower for word in ['website', 'ecommerce', 'online store', 'build']):
        return f"Excellent, {name}! Besides payment gateway, we also help build professional e-commerce websites optimized for your {business_type}. Complete solution with payment integration, inventory management, and analytics. Let's discuss your requirements!"

    elif any(word in query_lower for word in ['problem', 'issue', 'fix', 'solve', 'trouble']):
        return f"I understand you're facing some issues, {name}. Getepay's technical team is here to help! Common payment problems we solve include: failed transactions, integration issues, settlement delays, and payment method conflicts. Our 24/7 support team can diagnose and fix most issues within hours. Can you tell me more about the specific problem you're experiencing?"

    else:
        return f"Hi {name}! Thanks for your interest in Getepay. We're India's leading payment gateway with 99.9% uptime, competitive rates (1.5-2%), and support for all payment methods. How can we specifically help grow your {business_type} with our payment solutions?"

# Enhanced Sentiment Analysis with Bedrock
def analyze_sentiment_bedrock(text):
    """Use AWS Bedrock for advanced sentiment analysis with TextBlob fallback"""
    if not bedrock_client:
        # Use TextBlob as fallback
        return analyze_sentiment_textblob(text)

    try:
        prompt = f"""
        Analyze the sentiment of this customer message and provide detailed insights:

        Message: "{text}"

        Please provide:
        1. Overall sentiment (positive/negative/neutral)
        2. Sentiment score (-1 to 1)
        3. Confidence level (0 to 1)
        4. Brief explanation of the sentiment
        5. Customer intent (inquiry/complaint/interest/support)
        6. Urgency level (low/medium/high)

        Respond in JSON format:
        {{
            "sentiment": "positive/negative/neutral",
            "score": 0.0,
            "confidence": 0.0,
            "explanation": "brief explanation",
            "intent": "customer intent",
            "urgency": "low/medium/high"
        }}
        """

        body = json.dumps({
            "prompt": prompt,
            "max_tokens": 200,
            "temperature": 0.1
        })

        response = bedrock_client.invoke_model(
            body=body,
            modelId="anthropic.claude-3-sonnet-********-v1:0",
            accept="application/json",
            contentType="application/json"
        )

        response_body = json.loads(response.get('body').read())
        result_text = response_body.get('completion', '{}')

        # Parse JSON from response
        try:
            result = json.loads(result_text)
            return result
        except:
            # Fallback to TextBlob
            return analyze_sentiment_textblob(text)

    except Exception as e:
        # Fallback to TextBlob
        return analyze_sentiment_textblob(text)

# TextBlob sentiment analysis fallback
def analyze_sentiment_textblob(text):
    """Fallback sentiment analysis using TextBlob"""
    try:
        from textblob import TextBlob
        blob = TextBlob(text)
        score = blob.sentiment.polarity

        # Determine sentiment
        if score > 0.1:
            sentiment = "positive"
        elif score < -0.1:
            sentiment = "negative"
        else:
            sentiment = "neutral"

        # Determine intent based on keywords
        text_lower = text.lower()
        if any(word in text_lower for word in ['buy', 'purchase', 'price', 'cost', 'payment']):
            intent = "purchase"
        elif any(word in text_lower for word in ['help', 'support', 'problem', 'issue']):
            intent = "support"
        elif any(word in text_lower for word in ['interested', 'want', 'need', 'looking']):
            intent = "interest"
        elif any(word in text_lower for word in ['complaint', 'angry', 'frustrated', 'bad']):
            intent = "complaint"
        else:
            intent = "inquiry"

        # Determine urgency
        if any(word in text_lower for word in ['urgent', 'asap', 'immediately', 'emergency']):
            urgency = "high"
        elif any(word in text_lower for word in ['soon', 'quickly', 'fast']):
            urgency = "medium"
        else:
            urgency = "low"

        return {
            "sentiment": sentiment,
            "score": score,
            "confidence": 0.8,  # TextBlob confidence
            "explanation": f"TextBlob analysis: {sentiment} sentiment detected",
            "intent": intent,
            "urgency": urgency
        }

    except Exception as e:
        return {
            "sentiment": "neutral",
            "score": 0.0,
            "confidence": 0.5,
            "explanation": "Sentiment analysis unavailable",
            "intent": "inquiry",
            "urgency": "medium"
        }

def get_sentiment_display(sentiment_data):
    """Enhanced sentiment display using Bedrock analysis"""
    sentiment = sentiment_data.get("sentiment", "neutral")
    score = sentiment_data.get("score", 0.0)
    confidence = sentiment_data.get("confidence", 0.5)
    explanation = sentiment_data.get("explanation", "")
    intent = sentiment_data.get("intent", "inquiry")
    urgency = sentiment_data.get("urgency", "medium")

    if sentiment == "positive":
        if score > 0.5:
            return {
                "emoji": "😊", "label": "Very Positive", "color": "#28a745",
                "tip": f"Excellent! {explanation}. Customer intent: {intent}",
                "urgency": urgency, "confidence": confidence
            }
        else:
            return {
                "emoji": "🙂", "label": "Positive", "color": "#20c997",
                "tip": f"Good response! {explanation}. Customer intent: {intent}",
                "urgency": urgency, "confidence": confidence
            }
    elif sentiment == "negative":
        if score < -0.5:
            return {
                "emoji": "😞", "label": "Very Negative", "color": "#dc3545",
                "tip": f"⚠️ URGENT: {explanation}. Customer intent: {intent}",
                "urgency": urgency, "confidence": confidence
            }
        else:
            return {
                "emoji": "😕", "label": "Negative", "color": "#fd7e14",
                "tip": f"Attention needed: {explanation}. Customer intent: {intent}",
                "urgency": urgency, "confidence": confidence
            }
    else:
        return {
            "emoji": "😐", "label": "Neutral", "color": "#6c757d",
            "tip": f"Neutral response: {explanation}. Customer intent: {intent}",
            "urgency": urgency, "confidence": confidence
        }

# Enhanced lead scoring with Bedrock sentiment
def calculate_lead_score(user_info, sentiment_data):
    score = 50  # Base score

    # Email and phone provided
    if user_info.get('email'): score += 15
    if user_info.get('phone'): score += 15

    # Company information
    if user_info.get('company_name'): score += 10
    if user_info.get('business_type'): score += 10

    # Enhanced sentiment scoring
    sentiment_score = sentiment_data.get("score", 0.0)
    confidence = sentiment_data.get("confidence", 0.5)
    urgency = sentiment_data.get("urgency", "medium")
    intent = sentiment_data.get("intent", "inquiry")

    # Sentiment bonus (weighted by confidence)
    score += (sentiment_score * 20 * confidence)

    # Intent bonus
    intent_bonus = {
        "purchase": 15, "interest": 10, "inquiry": 5,
        "complaint": -5, "support": 0
    }
    score += intent_bonus.get(intent, 0)

    # Urgency bonus
    urgency_bonus = {"high": 10, "medium": 5, "low": 0}
    score += urgency_bonus.get(urgency, 0)

    return max(0, min(100, score))

# Enhanced lead data saving
def save_lead_data(session_id, messages, user_info, sentiment_data, lead_score):
    conversation_text = " | ".join([f"{msg['role']}: {msg['content']}" for msg in messages])

    new_data = {
        'session_id': session_id,
        'timestamp': datetime.now().isoformat(),
        'name': user_info.get('name', ''),
        'email': user_info.get('email', ''),
        'phone': user_info.get('phone', ''),
        'company_name': user_info.get('company_name', ''),
        'business_type': user_info.get('business_type', ''),
        'services_interested': user_info.get('services_interested', ''),
        'requirement_summary': user_info.get('requirement_summary', ''),
        'lead_score': lead_score,
        'sentiment_score': sentiment_data.get("score", 0.0),
        'sentiment_label': sentiment_data.get("sentiment", "neutral"),
        'sentiment_confidence': sentiment_data.get("confidence", 0.5),
        'customer_intent': sentiment_data.get("intent", "inquiry"),
        'urgency_level': sentiment_data.get("urgency", "medium"),
        'conversation_data': conversation_text
    }

    df = pd.read_csv(DATA_CSV) if os.path.exists(DATA_CSV) else pd.DataFrame()
    df = pd.concat([df, pd.DataFrame([new_data])], ignore_index=True)
    df.to_csv(DATA_CSV, index=False)

# Pre-typed genuine queries for users
def get_pretyped_queries():
    return [
        "What are your payment gateway charges and fees?",
        "How quickly can I integrate Getepay with my website?",
        "Do you support UPI, cards, and net banking?",
        "What's the settlement time for transactions?",
        "Can you help me build an e-commerce website?",
        "Do you provide 24/7 customer support?",
        "What security measures do you have in place?",
        "Can I get a demo of your payment gateway?",
        "What documents do I need for account setup?",
        "Do you offer volume discounts for high transactions?"
    ]

# Enhanced AI response using Bedrock
def get_ai_response_bedrock(user_message, user_info):
    """Get intelligent response from AWS Bedrock"""
    if not bedrock_client:
        return get_fallback_response(user_message, user_info)

    try:
        user_context = f"Customer: {user_info.get('name', 'Unknown')}, Business: {user_info.get('business_type', 'Unknown')}, Company: {user_info.get('company_name', 'Unknown')}"

        prompt = f"""
        You are a professional Getepay payment gateway consultant. Respond to the customer query with accurate, helpful information.

        Customer Context: {user_context}
        Customer Query: "{user_message}"

        Getepay Services:
        - Payment Gateway (1.5-2% charges, 99.9% uptime)
        - UPI, Cards, Net Banking, Wallets support
        - 24/7 customer support (+91 **********)
        - API integration within 24 hours
        - E-commerce website development
        - Business management tools
        - Instant settlements available
        - Volume discounts for high-transaction businesses

        Provide a professional, helpful response that addresses their query and encourages further engagement.
        Keep response under 150 words and include a follow-up question.
        """

        body = json.dumps({
            "prompt": prompt,
            "max_tokens": 200,
            "temperature": 0.7
        })

        response = bedrock_client.invoke_model(
            body=body,
            modelId="anthropic.claude-3-sonnet-********-v1:0",
            accept="application/json",
            contentType="application/json"
        )

        response_body = json.loads(response.get('body').read())
        return response_body.get('completion', get_fallback_response(user_message, user_info))

    except Exception as e:
        st.error(f"Bedrock AI response failed: {e}")
        return get_fallback_response(user_message, user_info)

# Fallback responses when Bedrock is not available
def get_fallback_response(user_message, user_info):
    message_lower = user_message.lower()
    name = user_info.get('name', 'there')

    if any(word in message_lower for word in ['payment', 'gateway', 'transaction']):
        return f"Hi {name}! Getepay offers secure payment gateway solutions with 99.9% uptime. We support UPI, cards, net banking, and wallets. Our competitive rates start at just 1.5-2% per transaction. Would you like to know more about our integration process?"

    elif any(word in message_lower for word in ['pricing', 'cost', 'price', 'fee', 'charge']):
        return f"Great question, {name}! Our pricing is very competitive - we charge only 1.5-2% per transaction with no setup fees. We also offer volume discounts for businesses with high transaction volumes. What's your expected monthly transaction volume?"

    elif any(word in message_lower for word in ['integration', 'api', 'setup', 'install']):
        return f"Integration is super easy, {name}! We provide ready-to-use APIs, plugins for popular platforms like WooCommerce, Shopify, and dedicated technical support. Our team can help you go live within 24 hours. Do you have a website or app already?"

    elif any(word in message_lower for word in ['support', 'help', 'assistance', 'contact']):
        return f"We've got you covered, {name}! We provide 24/7 customer support via phone, email, and chat. Our technical team is always ready to help. You can reach us at +91 ********** or <EMAIL>. What specific help do you need?"

    elif any(word in message_lower for word in ['security', 'safe', 'secure', 'protection']):
        return f"Security is our top priority, {name}! We use 256-bit SSL encryption, PCI DSS compliance, and advanced fraud detection. All transactions are processed through secure channels with real-time monitoring. Your customers' data is completely safe with us."

    elif any(word in message_lower for word in ['settlement', 'payout', 'money', 'transfer']):
        return f"Good question, {name}! We offer flexible settlement options - T+1 (next day), T+2, or instant settlements. Most businesses receive their money within 24 hours. We also provide detailed transaction reports and analytics. What settlement preference works best for your business?"

    else:
        return f"Thank you for your interest, {name}! Getepay provides complete payment solutions for businesses of all sizes. We offer payment gateway, business management tools, website/app development, and 24/7 support. How can we help your {user_info.get('business_type', 'business')} grow?"

# Voice input functionality (Simplified for demo)
def process_voice_input():
    """Simplified voice input - returns demo text for now"""
    # This is a placeholder - in production, you would integrate with:
    # - Web Speech API
    # - Google Speech-to-Text
    # - AWS Transcribe
    # - Azure Speech Services

    demo_queries = [
        "What are your payment gateway charges?",
        "How can I integrate Getepay with my website?",
        "Do you support UPI payments?",
        "Can you help me with e-commerce setup?",
        "What's your customer support like?"
    ]

    import random
    return random.choice(demo_queries)

# WebSocket handler for real-time updates
async def websocket_handler(websocket, path):
    """Handle WebSocket connections for real-time updates"""
    try:
        async for message in websocket:
            data = json.loads(message)
            # Process real-time updates here
            response = {"status": "received", "data": data}
            await websocket.send(json.dumps(response))
    except Exception as e:
        print(f"WebSocket error: {e}")

# Lead analytics functions
def get_lead_analytics():
    """Get comprehensive lead analytics"""
    if not os.path.exists(DATA_CSV):
        return None

    df = pd.read_csv(DATA_CSV)
    if df.empty:
        return None

    analytics = {
        'total_leads': len(df),
        'hot_leads': len(df[df['lead_score'] >= 80]) if 'lead_score' in df.columns else 0,
        'warm_leads': len(df[(df['lead_score'] >= 60) & (df['lead_score'] < 80)]) if 'lead_score' in df.columns else 0,
        'cold_leads': len(df[df['lead_score'] < 60]) if 'lead_score' in df.columns else 0,
        'avg_sentiment': df['sentiment_score'].mean() if 'sentiment_score' in df.columns else 0.0,
        'positive_sentiment': len(df[df['sentiment_score'] > 0.1]) if 'sentiment_score' in df.columns else 0,
        'negative_sentiment': len(df[df['sentiment_score'] < -0.1]) if 'sentiment_score' in df.columns else 0,
        'business_types': df['business_type'].value_counts().to_dict() if 'business_type' in df.columns else {},
        'recent_leads': df.tail(5).to_dict('records'),
        'high_urgency': len(df[df['urgency_level'] == 'high']) if 'urgency_level' in df.columns else 0,
        'conversion_rate': (len(df[df['lead_score'] >= 80]) / len(df) * 100) if len(df) > 0 and 'lead_score' in df.columns else 0
    }

    return analytics

def main():
    # Page config
    st.set_page_config(
        page_title="Getepay AI Assistant - Advanced",
        page_icon="💳",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize session state for page navigation
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "Chat"
    
    # Initialize
    initialize_csv()
    
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    if 'user_info' not in st.session_state:
        st.session_state.user_info = {}
    if 'show_form' not in st.session_state:
        st.session_state.show_form = True

    # Enhanced Custom CSS
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        color: white;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    .chat-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        height: 600px;
        overflow-y: auto;
        border: 1px solid #e0e0e0;
    }
    .sentiment-panel {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
    }
    .user-message {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 1rem;
        border-radius: 20px;
        margin: 0.8rem 0;
        margin-left: 15%;
        box-shadow: 0 2px 10px rgba(0,123,255,0.3);
        animation: slideInRight 0.3s ease-out;
    }
    .bot-message {
        background: linear-gradient(135deg, #f1f3f4 0%, #e9ecef 100%);
        color: #333;
        padding: 1rem;
        border-radius: 20px;
        margin: 0.8rem 0;
        margin-right: 15%;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        animation: slideInLeft 0.3s ease-out;
    }
    .sentiment-card {
        text-align: center;
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
        color: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        transition: transform 0.3s ease;
    }
    .sentiment-card:hover {
        transform: translateY(-5px);
    }
    .query-button {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 0.8rem 1.2rem;
        border-radius: 25px;
        margin: 0.3rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(40,167,69,0.3);
    }
    .query-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40,167,69,0.4);
    }
    .voice-button {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 1rem;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(220,53,69,0.3);
    }
    .voice-button:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(220,53,69,0.4);
    }
    .analytics-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border-left: 5px solid #007bff;
    }
    @keyframes slideInRight {
        from { opacity: 0; transform: translateX(50px); }
        to { opacity: 1; transform: translateX(0); }
    }
    @keyframes slideInLeft {
        from { opacity: 0; transform: translateX(-50px); }
        to { opacity: 1; transform: translateX(0); }
    }
    .stButton > button {
        border-radius: 25px !important;
        transition: all 0.3s ease !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Navigation in Sidebar
    with st.sidebar:
        st.markdown("### 🧭 Navigation")

        # Page selection
        page_options = ["💬 Chat Assistant", "📊 Analytics Dashboard", "⚙️ Settings"]
        selected_page = st.selectbox("Select Page", page_options, index=0)

        # Update session state based on selection
        if "Chat" in selected_page:
            st.session_state.current_page = "Chat"
        elif "Analytics" in selected_page:
            st.session_state.current_page = "Analytics"
        elif "Settings" in selected_page:
            st.session_state.current_page = "Settings"

        st.markdown("---")

        # Quick stats in sidebar
        analytics = get_lead_analytics()
        if analytics:
            st.markdown("### 📈 Quick Stats")
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Total", analytics['total_leads'])
                st.metric("Hot", analytics['hot_leads'])
            with col2:
                st.metric("Rate", f"{analytics['conversion_rate']:.1f}%")
                st.metric("Sentiment", f"{analytics['avg_sentiment']:.2f}")

        st.markdown("---")
        st.markdown("### 🚀 Quick Actions")
        if st.button("� Refresh Data", use_container_width=True):
            st.rerun()

        if st.button("📊 View Full Analytics", use_container_width=True):
            st.session_state.current_page = "Analytics"
            st.rerun()

    # Page routing
    if st.session_state.current_page == "Chat":
        render_chat_page()
    elif st.session_state.current_page == "Analytics":
        render_analytics_page()
    elif st.session_state.current_page == "Settings":
        render_settings_page()

# Chat Page
def render_chat_page():
    # Enhanced Header
    st.markdown("""
    <div class="main-header">
        <h1>� Getepay AI Chat Assistant</h1>
        <p>Powered by AWS Bedrock | Real-time Sentiment Analysis</p>
        <div style="margin-top: 1rem;">
            <span style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; margin: 0.2rem;">
                💳 Payment Gateway
            </span>
            <span style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; margin: 0.2rem;">
                🤖 AI-Powered
            </span>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Main layout
    col1, col2 = st.columns([2.5, 1.5])

    with col1:
        st.markdown("### 💬 Intelligent Chat Assistant")

        # Initialize user info if not exists (skip form for demo)
        if 'user_info' not in st.session_state:
            st.session_state.user_info = {
                'name': 'Deepak Garg',
                'email': '<EMAIL>',
                'phone': '+91 9876543210',
                'company_name': 'Deepak Restaurant',
                'business_type': 'Restaurant',
                'services_interested': 'Payment Gateway'
            }
            st.session_state.show_form = False
        
        # Enhanced Chat interface - Display messages directly without white box
        st.markdown("#### 💬 Conversation")

        # Initialize messages if not exists
        if 'messages' not in st.session_state:
            st.session_state.messages = [
                {"role": "assistant", "content": f"Hello {st.session_state.user_info['name']}! 🎉 Welcome to Getepay's AI-powered assistant. I'm here to help you with payment gateway solutions, business growth, and technical support. How can I assist your {st.session_state.user_info['business_type']} today?"}
            ]

        # Display chat messages directly without container
        for message in st.session_state.messages:
            if message["role"] == "user":
                st.markdown(f"""
                <div style="display: flex; justify-content: flex-end; margin: 10px 0;">
                    <div style="background: linear-gradient(135deg, #007bff, #0056b3); color: white;
                               padding: 12px 18px; border-radius: 18px 18px 5px 18px; max-width: 70%;
                               box-shadow: 0 2px 10px rgba(0,123,255,0.3);">
                        <strong>👤</strong> {message["content"]}
                    </div>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div style="display: flex; justify-content: flex-start; margin: 10px 0;">
                    <div style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); color: #333;
                               padding: 12px 18px; border-radius: 18px 18px 18px 5px; max-width: 70%;
                               box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid #28a745;">
                        <strong>🤖</strong> {message["content"]}
                    </div>
                </div>
                """, unsafe_allow_html=True)

        # Pre-typed query suggestions AFTER conversation (hide after first use)
        if 'questions_used' not in st.session_state:
            st.session_state.questions_used = False

        if not st.session_state.questions_used:
            st.markdown("#### 💡 Quick Questions (Click to ask)")
            queries = get_pretyped_queries()

            # Display queries in a grid
            cols = st.columns(2)
            for i, query in enumerate(queries):
                with cols[i % 2]:
                    if st.button(f"💬 {query}", key=f"query_{i}", use_container_width=True):
                        # Mark questions as used
                        st.session_state.questions_used = True

                        # Add user message
                        st.session_state.messages.append({"role": "user", "content": query})

                        # Get AI response using Bedrock with conversation context
                        response = generate_bedrock_response(query, st.session_state.user_info, st.session_state.messages)
                        st.session_state.messages.append({"role": "assistant", "content": response})

                        # Enhanced sentiment analysis using Bedrock
                        sentiment_data = analyze_sentiment_bedrock(query)
                        lead_score = calculate_lead_score(st.session_state.user_info, sentiment_data)

                        # Save enhanced data
                        save_lead_data(
                            st.session_state.session_id,
                            st.session_state.messages,
                            st.session_state.user_info,
                            sentiment_data,
                            lead_score
                        )

                        st.rerun()

        # Voice input section removed
        # Chat input - Direct input without voice features
        user_input = st.chat_input("💬 Type your message here...")

        if user_input:
            # Mark questions as used when user types their own message
            st.session_state.questions_used = True

            # Add user message
            st.session_state.messages.append({"role": "user", "content": user_input})

            # Generate Bedrock response with conversation context
            response = generate_bedrock_response(user_input, st.session_state.user_info, st.session_state.messages)
            st.session_state.messages.append({"role": "assistant", "content": response})

            # Enhanced sentiment analysis using Bedrock
            sentiment_data = analyze_sentiment_bedrock(user_input)
            lead_score = calculate_lead_score(st.session_state.user_info, sentiment_data)

            # Save enhanced data
            save_lead_data(
                st.session_state.session_id,
                st.session_state.messages,
                st.session_state.user_info,
                sentiment_data,
                lead_score
            )

            st.rerun()
    
    with col2:
        st.markdown("### 🧠 AWS Bedrock Sentiment Analysis")

        if st.session_state.messages:
            # Get user messages for sentiment analysis
            user_messages = [msg["content"] for msg in st.session_state.messages if msg["role"] == "user"]

            if user_messages:
                # Latest message sentiment using Bedrock
                latest_sentiment_data = analyze_sentiment_bedrock(user_messages[-1])
                sentiment_info = get_sentiment_display(latest_sentiment_data)

                st.markdown(f"""
                <div class="sentiment-card" style="background: {sentiment_info['color']};">
                    <h3>🎯 Latest Message Analysis</h3>
                    <div style="font-size: 3rem;">{sentiment_info['emoji']}</div>
                    <h4>{sentiment_info['label']}</h4>
                    <p><strong>Score:</strong> {latest_sentiment_data.get('score', 0):.2f}</p>
                    <p><strong>Confidence:</strong> {latest_sentiment_data.get('confidence', 0):.0%}</p>
                    <p><strong>Intent:</strong> {latest_sentiment_data.get('intent', 'Unknown')}</p>
                    <p><strong>Urgency:</strong> {latest_sentiment_data.get('urgency', 'Medium')}</p>
                </div>
                """, unsafe_allow_html=True)

                # Enhanced AI Tip with Bedrock insights
                st.markdown(f"""
                <div style="background: linear-gradient(135deg, #17a2b8, #138496);
                           padding: 1rem; border-radius: 10px; color: white; margin: 1rem 0;">
                    <h4>🤖 AI Insights</h4>
                    <p><strong>Recommendation:</strong> {sentiment_info['tip']}</p>
                    <p><strong>Analysis:</strong> {latest_sentiment_data.get('explanation', 'No explanation available')}</p>
                </div>
                """, unsafe_allow_html=True)

                # Overall conversation sentiment
                overall_text = " ".join(user_messages)
                overall_sentiment_data = analyze_sentiment_bedrock(overall_text)
                overall_info = get_sentiment_display(overall_sentiment_data)

                st.markdown(f"""
                <div class="sentiment-card" style="background: {overall_info['color']};">
                    <h4>📈 Overall Conversation</h4>
                    <div style="font-size: 2rem;">{overall_info['emoji']}</div>
                    <p><strong>{overall_info['label']}</strong></p>
                    <p>Score: {overall_sentiment_data.get('score', 0):.2f}</p>
                    <p>Confidence: {overall_sentiment_data.get('confidence', 0):.0%}</p>
                </div>
                """, unsafe_allow_html=True)

                # Enhanced Lead Score
                if st.session_state.user_info:
                    lead_score = calculate_lead_score(st.session_state.user_info, latest_sentiment_data)
                    score_color = "#28a745" if lead_score >= 80 else "#ffc107" if lead_score >= 60 else "#dc3545"
                    lead_status = "🔥 Hot Lead" if lead_score >= 80 else "🌡️ Warm Lead" if lead_score >= 60 else "❄️ Cold Lead"

                    st.markdown(f"""
                    <div class="sentiment-card" style="background: {score_color};">
                        <h4>🎯 Lead Score</h4>
                        <div style="font-size: 3rem;">{int(lead_score)}</div>
                        <p><strong>{lead_status}</strong></p>
                        <div style="background: rgba(255,255,255,0.2); padding: 0.5rem; border-radius: 5px; margin-top: 0.5rem;">
                            <small>Based on: Profile completeness, sentiment, intent, and urgency</small>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

                # Sentiment History Chart
                if len(user_messages) > 1:
                    st.markdown("#### 📊 Sentiment Journey")
                    sentiment_scores = []
                    for msg in user_messages:
                        score = analyze_sentiment_bedrock(msg).get('score', 0)
                        sentiment_scores.append(score)

                    # Create a simple line chart
                    chart_data = pd.DataFrame({
                        'Message': range(1, len(sentiment_scores) + 1),
                        'Sentiment': sentiment_scores
                    })
                    st.line_chart(chart_data.set_index('Message'))

        else:
            st.markdown("""
            <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                       border-radius: 15px; color: #6c757d;">
                <h3>🚀 Ready for Analysis!</h3>
                <p>Start chatting to see real-time sentiment analysis powered by AWS Bedrock AI</p>
                <div style="margin-top: 1rem;">
                    <span style="font-size: 2rem;">🧠</span>
                    <span style="font-size: 2rem;">📊</span>
                    <span style="font-size: 2rem;">🎯</span>
                </div>
            </div>
            """, unsafe_allow_html=True)

# Analytics Dashboard Page
def render_analytics_page():
    st.markdown("""
    <div class="main-header">
        <h1>📊 Live Analytics Dashboard</h1>
        <p>Real-time Lead Management & Business Intelligence</p>
    </div>
    """, unsafe_allow_html=True)

    analytics = get_lead_analytics()

    if not analytics:
        st.markdown("""
        <div style="text-align: center; padding: 3rem; background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                   border-radius: 15px; color: #6c757d;">
            <h2>📈 No Data Available</h2>
            <p>Start chatting to generate analytics data!</p>
            <div style="margin-top: 2rem;">
                <span style="font-size: 3rem;">📊</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("🚀 Go to Chat", use_container_width=True):
            st.session_state.current_page = "Chat"
            st.rerun()
        return

    # Key Metrics Row
    st.markdown("### 🎯 Key Performance Indicators")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="analytics-card" style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
            <h2>{analytics['total_leads']}</h2>
            <p>Total Leads</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="analytics-card" style="background: linear-gradient(135deg, #dc3545, #fd7e14); color: white;">
            <h2>{analytics['hot_leads']}</h2>
            <p>Hot Leads</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div class="analytics-card" style="background: linear-gradient(135deg, #007bff, #6610f2); color: white;">
            <h2>{analytics['conversion_rate']:.1f}%</h2>
            <p>Conversion Rate</p>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown(f"""
        <div class="analytics-card" style="background: linear-gradient(135deg, #ffc107, #fd7e14); color: white;">
            <h2>{analytics['avg_sentiment']:.2f}</h2>
            <p>Avg Sentiment</p>
        </div>
        """, unsafe_allow_html=True)

    # Lead Distribution
    st.markdown("### 🔥 Lead Distribution")
    col1, col2 = st.columns(2)

    with col1:
        # Lead score distribution
        lead_data = {
            'Hot Leads (80+)': analytics['hot_leads'],
            'Warm Leads (60-79)': analytics['warm_leads'],
            'Cold Leads (<60)': analytics['cold_leads']
        }
        st.bar_chart(lead_data)

    with col2:
        # Sentiment distribution
        sentiment_data = {
            'Positive': analytics['positive_sentiment'],
            'Negative': analytics['negative_sentiment'],
            'Neutral': analytics['total_leads'] - analytics['positive_sentiment'] - analytics['negative_sentiment']
        }
        st.bar_chart(sentiment_data)

    # Business Types Analysis
    st.markdown("### 🏢 Business Type Analysis")
    if analytics['business_types']:
        business_df = pd.DataFrame(list(analytics['business_types'].items()),
                                 columns=['Business Type', 'Count'])
        st.bar_chart(business_df.set_index('Business Type'))

    # Recent Leads Table
    st.markdown("### 🕒 Recent Leads")
    if analytics['recent_leads']:
        leads_df = pd.DataFrame(analytics['recent_leads'])
        # Select relevant columns
        display_cols = ['name', 'email', 'business_type', 'lead_score', 'sentiment_score', 'timestamp']
        available_cols = [col for col in display_cols if col in leads_df.columns]
        st.dataframe(leads_df[available_cols], use_container_width=True)

    # Urgency Alerts
    if analytics['high_urgency'] > 0:
        st.error(f"⚠️ {analytics['high_urgency']} High Urgency Leads Need Immediate Attention!")

    # Export Section
    st.markdown("### 📤 Data Export")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("📊 Export Analytics Report", use_container_width=True):
            # Create analytics report
            report_data = {
                'metric': ['Total Leads', 'Hot Leads', 'Conversion Rate', 'Avg Sentiment'],
                'value': [analytics['total_leads'], analytics['hot_leads'],
                         f"{analytics['conversion_rate']:.1f}%", f"{analytics['avg_sentiment']:.2f}"]
            }
            report_df = pd.DataFrame(report_data)
            csv = report_df.to_csv(index=False)
            st.download_button(
                label="⬇️ Download Analytics CSV",
                data=csv,
                file_name=f"getepay_analytics_{datetime.now().strftime('%Y%m%d_%H%M')}.csv",
                mime="text/csv"
            )

    with col2:
        if st.button("📋 Export All Leads", use_container_width=True):
            if os.path.exists(DATA_CSV):
                df = pd.read_csv(DATA_CSV)
                csv = df.to_csv(index=False)
                st.download_button(
                    label="⬇️ Download All Leads CSV",
                    data=csv,
                    file_name=f"getepay_leads_{datetime.now().strftime('%Y%m%d_%H%M')}.csv",
                    mime="text/csv"
                )

# Settings Page
def render_settings_page():
    st.markdown("""
    <div class="main-header">
        <h1>⚙️ Settings & Configuration</h1>
        <p>Customize your Getepay AI Assistant</p>
    </div>
    """, unsafe_allow_html=True)

    st.markdown("### 🔧 System Configuration")

    # AWS Bedrock Status
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 🧠 AI Configuration")
        bedrock_status = "✅ Connected" if bedrock_client else "❌ Not Available"
        st.info(f"AWS Bedrock Status: {bedrock_status}")

        if not bedrock_client:
            st.warning("Using TextBlob fallback for sentiment analysis")
            st.markdown("""
            **To enable AWS Bedrock:**
            1. Configure AWS credentials
            2. Ensure Bedrock access in your region
            3. Restart the application
            """)

    with col2:
        st.markdown("#### 📊 Data Configuration")
        if os.path.exists(DATA_CSV):
            df = pd.read_csv(DATA_CSV)
            st.success(f"Data file exists: {len(df)} records")
        else:
            st.warning("No data file found")

        if st.button("🗑️ Clear All Data", type="secondary"):
            if os.path.exists(DATA_CSV):
                os.remove(DATA_CSV)
                st.success("Data cleared successfully!")
                st.rerun()

    # Application Settings
    st.markdown("### 🎨 Application Settings")

    # Theme settings (placeholder)
    theme = st.selectbox("Choose Theme", ["Professional Blue", "Modern Green", "Classic Purple"])

    # Notification settings
    st.markdown("#### 🔔 Notifications")
    enable_alerts = st.checkbox("Enable high urgency alerts", value=True)
    enable_exports = st.checkbox("Enable data export features", value=True)

    # Performance settings
    st.markdown("#### ⚡ Performance")
    max_messages = st.slider("Max messages to display", 10, 100, 50)
    auto_refresh = st.checkbox("Auto-refresh analytics", value=False)

    if st.button("💾 Save Settings", type="primary"):
        st.success("Settings saved successfully!")

    # System Information
    st.markdown("### ℹ️ System Information")
    st.info(f"""
    **Application Version:** 2.0.0 Advanced
    **Python Version:** {os.sys.version.split()[0]}
    **Streamlit Version:** {st.__version__}
    **Data File:** {DATA_CSV}
    **AWS Region:** {AWS_REGION}
    """)

if __name__ == "__main__":
    main()
